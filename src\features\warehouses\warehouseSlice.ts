import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { store } from '../../app/store';

// Define the type of each warehouse
export interface Warehouse {
    id: string;
    name: string;
    location: string;
    status: string;
}

// Define the shape of the slice state
interface WarehouseState {
    warehouses: Warehouse[];
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: WarehouseState = {
    warehouses: [],
    loading: false,
    error: null,
};

// Async thunk to fetch warehouses
export const fetchWarehouses = createAsyncThunk(
    'warehouses/fetchWarehouses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get('/warehouses');
            return response.data; // should be an array of warehouses
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch warehouses');
        }
    }
);



// Slice
const warehouseSlice = createSlice({
    name: 'warehouses',
    initialState,
    reducers: {
        // add your own reducers here if needed
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchWarehouses.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchWarehouses.fulfilled, (state, action: PayloadAction<Warehouse[]>) => {
                state.loading = false;
                state.warehouses = action.payload;
            })
            .addCase(fetchWarehouses.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            });
    },
});

// Export the reducer
export default warehouseSlice.reducer;
