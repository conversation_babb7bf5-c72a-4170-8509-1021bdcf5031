import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StyleSheet,
    View,
    ViewProps,
    Platform,
    StatusBar as RNStatusBar,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import useTheme from '../theme/useTheme';
import { useThemeMode } from '../theme/ThemeContext';

type Props = {
    scrollable?: boolean;
    children: React.ReactNode;
} & ViewProps;

export default function ThemedView({ scrollable = false, children, style, ...rest }: Props) {
    const theme = useTheme();
    const { resolvedTheme } = useThemeMode();
    const Container = scrollable ? ScrollView : View;

    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
            <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} />
            <Container style={[styles.container, { backgroundColor: theme.background }, style]} {...rest}>
                {children}
            </Container>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        paddingTop: Platform.OS === 'android' ? RNStatusBar.currentHeight || 0 : 0,
    },
    container: {
        flex: 1,
        padding: 20,
        paddingBottom: 40,
    },
});
