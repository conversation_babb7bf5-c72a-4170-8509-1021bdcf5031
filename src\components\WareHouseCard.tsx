
import React from 'react';
import { GestureResponderEvent, StyleSheet, TouchableOpacity, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { FontAwesome6 } from '@expo/vector-icons';

type Props = {
    warehouse: any;
    onPress: (event: GestureResponderEvent) => void;
};

export default function WareHouseCard({ warehouse, onPress }: Props) {
    const theme = useTheme();

    return (
        <TouchableOpacity
            onPress={onPress}
            style={[styles.button, { backgroundColor: theme.cardBackground }]}
        >
            <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>

                <View style={{ display: 'flex', flexDirection: 'column', gap: 4, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    <ThemedText variant='title'>{warehouse.name}</ThemedText>
                    <ThemedText variant='body'>{warehouse.location}</ThemedText>
                </View>
                <View style={{ display: 'flex', flexDirection: 'column', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                    <FontAwesome6 name="cubes-stacked" size={30} color={theme.text} />
                    <ThemedText variant='body'>{warehouse.collections}</ThemedText>
                </View>
            </View>

        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        alignItems: 'center',
    },

});
