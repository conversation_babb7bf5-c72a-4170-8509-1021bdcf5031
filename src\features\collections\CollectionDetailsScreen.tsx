import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Feather } from '@expo/vector-icons';

import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { loadProgress } from '../../helpers/storage';
import CollectionDetailsCard from '../../components/CollectionDetailsCard';
import OrderGridItem from '../../components/OrderGridItem';
import { Modalize } from 'react-native-modalize';
import { OrderDetailsModal } from '../../components/OrderDetailsModal';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withDelay,
    Easing,
} from 'react-native-reanimated';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders } from '../orders/orderSlice';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import { Order, RootStackParamList } from '../../navigation/type';

type CollectionDetailsRouteProp = RouteProp<RootStackParamList, 'CollectionDetails'>;
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'CollectionDetails'>;

const AnimatedOrderItem = ({ order, index, onPress }: { order: Order; index: number; onPress: () => void }) => {
    const translateY = useSharedValue(20);

    useEffect(() => {
        translateY.value = withDelay(
            index * 100,
            withTiming(0, { duration: 400, easing: Easing.out(Easing.ease) })
        );
    }, []);

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
    }));

    return (
        <Animated.View style={animatedStyle}>
            <OrderGridItem order={order} onPress={onPress} />
        </Animated.View>
    );
};

export default function CollectionDetailsScreen() {
    const { params: { collection } } = useRoute<CollectionDetailsRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const theme = useTheme();
    const { loading: ordersLoading, orders } = useSelector((state: RootState) => state.orders);
    const [expandedSkus, setExpandedSkus] = useState<{ [sku: string]: boolean }>({});
    const [refreshing, setRefreshing] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
    const modalizeRef = useRef<Modalize>(null);

    const onOpen = (item: Order) => {
        setSelectedOrder(item);
        modalizeRef.current?.open();
    };

    useFocusEffect(
        useCallback(() => {
            dispatch(fetchOrders(collection.id));
        }, [dispatch, collection.id])
    );

    const onRefresh = async () => {
        setRefreshing(true);
        await dispatch(fetchOrders(collection.id));
        setRefreshing(false);
    };


    const toggleSku = (sku: string) => {
        setExpandedSkus((prev) => ({ ...prev, [sku]: !prev[sku] }));
    };

    // Sort and group orders by SKU
    const groupedBySku = orders?.slice()
        .sort((a, b) => a.sku.localeCompare(b.sku))
        .reduce((acc: { [sku: string]: Order[] }, order) => {
            if (!acc[order.sku]) acc[order.sku] = [];
            acc[order.sku].push(order);
            return acc;
        }, {}) ?? {};

    const skuGroups = Object.entries(groupedBySku).map(([sku, orders]) => ({ sku, orders }));

    // Determine if all products of all orders in a group are gathered
    const isGroupGathered = (orders: Order[]) =>
        orders.every(order =>
            order.ordersProducts.length > 0 &&
            order.ordersProducts.every(product => product.status === 'gathered')
        );

    const nextIndex = skuGroups.findIndex(group => !isGroupGathered(group.orders));

    const distributeOrdersBalanced = (orders: Order[]) => {
        const left: Order[] = [];
        const right: Order[] = [];
        let leftCount = 0;
        let rightCount = 0;

        const sortedOrders = [...orders].sort(
            (a, b) => b.ordersProducts.length - a.ordersProducts.length
        );

        for (const order of sortedOrders) {
            const weight = order.ordersProducts.length;
            if (leftCount <= rightCount) {
                left.push(order);
                leftCount += weight;
            } else {
                right.push(order);
                rightCount += weight;
            }
        }

        left.sort((a, b) => a.orderNum.localeCompare(b.orderNum));
        right.sort((a, b) => a.orderNum.localeCompare(b.orderNum));

        return { left, right };
    };

    return (
        <ScrollView
            refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.primary} />
            }
        >
            <ThemedView>
                {/* Header */}
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
                    <TouchableOpacity onPress={() => navigation.goBack()} style={{ height: 34 }}>
                        <AntDesign name="arrowleft" size={30} color={theme.text} />
                    </TouchableOpacity>
                    <ThemedText style={{ fontSize: 24 }} variant="title">{collection.name}</ThemedText>
                </View>

                {/* Info Card */}
                <CollectionDetailsCard collection={collection} />

                {/* Fulfillment Buttons */}
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 12, gap: 12 }}>
                    <TouchableOpacity
                        style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.success, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                    >
                        <Feather name="printer" size={24} color="white" />
                        <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>Print Details</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={() => navigation.navigate('Fulfillment', { collectionId: collection.id, orders })}
                        style={{ flexDirection: 'row', alignItems: 'center', gap: 6, backgroundColor: theme.primary, paddingVertical: 4, paddingHorizontal: 8, borderRadius: 8 }}
                    >
                        <ThemedText variant="title" style={{ fontSize: 16, color: 'white' }}>Start Fulfillment</ThemedText>
                        <AntDesign name="arrowright" size={18} color="white" />
                    </TouchableOpacity>
                </View>

                {/* Orders by SKU */}
                {ordersLoading && <ThreeDotsSkeleton />}
                {!ordersLoading && skuGroups.length === 0 && (
                    <ThemedText variant="title">No orders found</ThemedText>
                )}
                {skuGroups.map((group, index) => {
                    const isGathered = isGroupGathered(group.orders);
                    const isNext = index === nextIndex;
                    const canExpand = isGathered || isNext;
                    const isExpanded = expandedSkus[group.sku];

                    return (
                        <View key={group.sku} style={{ marginBottom: 16 }}>
                            {/* Accordion Header */}
                            <TouchableOpacity
                                onPress={() => {
                                    if (canExpand) toggleSku(group.sku);
                                }}
                                disabled={!canExpand}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: 8,
                                    opacity: isExpanded || canExpand ? 1 : 0.5,
                                }}
                            >
                                <ThemedText variant="body">SKU: {group.sku}</ThemedText>
                                {isGathered && <Feather name="check-circle" size={15} color={theme.success} />}
                                <View style={{ height: 1, flex: 1, backgroundColor: isGathered ? theme.success : theme.text }} />
                                <AntDesign
                                    name={isExpanded ? 'up' : 'down'}
                                    size={18}
                                    color={canExpand ? (isGathered ? theme.success : theme.text) : 'gray'}
                                />
                            </TouchableOpacity>

                            {/* Accordion Body */}
                            {isExpanded && (() => {
                                const { left, right } = distributeOrdersBalanced(group.orders);
                                return (
                                    <View style={{ flexDirection: 'row', gap: 10, marginTop: 12 }}>
                                        <View style={{ flex: 1, gap: 10 }}>
                                            {left.map((item, index) => (
                                                <AnimatedOrderItem key={item.id} order={item} index={index} onPress={() => onOpen(item)} />
                                            ))}
                                        </View>
                                        <View style={{ flex: 1, gap: 10 }}>
                                            {right.map((item, index) => (
                                                <AnimatedOrderItem key={item.id} order={item} index={index} onPress={() => onOpen(item)} />
                                            ))}
                                        </View>
                                    </View>
                                );
                            })()}
                        </View>
                    );
                })}

                <OrderDetailsModal ref={modalizeRef} order={selectedOrder} />
            </ThemedView>
        </ScrollView>
    );
}
