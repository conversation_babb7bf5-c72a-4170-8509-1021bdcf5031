import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../features/auth/LoginScreen';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import WarehouseListScreen from '../features/warehouses/WarehouseListScreen';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import { Order, RootStackParamList } from './type';
import AppSplashScreen from '../screens/SplashScreen';


const Stack = createNativeStackNavigator<RootStackParamList>();

export default function AppNavigator() {
    const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

    return (
        <NavigationContainer>
            <Stack.Navigator screenOptions={{ headerShown: false }}>
                {isLoggedIn ? (
                    <>
                        <Stack.Screen name="Warehouses" component={WarehouseListScreen} />
                        <Stack.Screen name="Collections" component={CollectionListScreen} />
                        <Stack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
                        <Stack.Screen name="Fulfillment" component={FulfillmentScreen} />

                    </>
                ) : (<>
                    <Stack.Screen name="Splash" component={AppSplashScreen} />
                    <Stack.Screen name="Login" component={LoginScreen} />
                </>)}
            </Stack.Navigator>
        </NavigationContainer>
    );
}
