import React, { useEffect, useState, useCallback } from 'react';
import { FlatList, TouchableOpacity, StyleSheet, View } from 'react-native';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import WareHouseCard from '../../components/WareHouseCard';
import { MaterialIcons } from '@expo/vector-icons';
import { logout } from '../auth/authSlice';
import { useDispatch, useSelector } from 'react-redux';
import { clearAuthData } from '../../utils/authStorage';
import { RootState, AppDispatch } from '../../app/store';
import { fetchWarehouses, Warehouse } from './warehouseSlice';
import PageTitleSkeleton from '../../components/PageTitleSkeleton';

type RootStackParamList = {
    Collections: { warehouse: Warehouse };
};

export default function WarehouseListScreen() {
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { warehouses, loading } = useSelector((state: RootState) => state.warehouses);

    const handleLogout = async () => {
        dispatch(logout());
        await clearAuthData();
    };

    const onRefresh = useCallback(() => {
        dispatch(fetchWarehouses());
    }, [dispatch]);

    useEffect(() => {
        dispatch(fetchWarehouses());
    }, [dispatch]);

    return (
        <ThemedView>
            <View style={styles.header}>
                {!loading ? <ThemedText style={styles.title} variant="title">
                    {warehouses.length} Warehouses
                </ThemedText> :
                    <PageTitleSkeleton />
                }
                <TouchableOpacity onPress={handleLogout} style={{ height: 34 }}>
                    <MaterialIcons name="logout" size={30} color="red" />
                </TouchableOpacity>
            </View>

            <FlatList
                data={warehouses}
                keyExtractor={(item) => item.id.toString()}
                ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
                renderItem={({ item }) => (
                    <WareHouseCard
                        warehouse={item}
                        onPress={() => navigation.navigate('Collections', { warehouse: item })}
                    />
                )}
                refreshing={loading}
                onRefresh={onRefresh}
            />
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    header: {
        width: '100%',
        marginBottom: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    title: {
        fontSize: 24,
        marginBottom: 16,
        height: 30,
    },
});
