import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View, FlatList, TouchableOpacity, Dimensions } from 'react-native';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import CollectionCard from '../../components/CollectionCard';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign } from '@expo/vector-icons';
import useTheme from '../../theme/useTheme';
import { Swipeable } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../app/store';
import { fetchCollectionsByWarehouseId } from './CollectionsThunks';
import PageTitleSkeleton from '../../components/PageTitleSkeleton';

type RootStackParamList = {
    Collections: { warehouse: any };
    CollectionDetails: { collection: any };
};

type CollectionsRouteProp = RouteProp<RootStackParamList, 'Collections'>;

export default function CollectionListScreen() {
    const dispatch = useDispatch<AppDispatch>();
    const route = useRoute<CollectionsRouteProp>();
    const { warehouse } = route.params;
    const theme = useTheme();
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { collections, loading } = useSelector((state: RootState) => state.collections);

    // const [collections, setCollections] = useState([
    //     { id: '1', name: 'Kreepta Collection', num: '25467', createdAt: '2022-01-01', status: 'created', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 4 },
    //     { id: '2', name: 'Summer Fashion', num: '25468', createdAt: '2022-02-15', status: 'shipped', shippingType: 'pickup', shippedAt: '2022-02-20', shippedBy: 'John Doe', orders: 12 },
    //     { id: '3', name: 'Winter Essentials', num: '25469', createdAt: '2022-03-10', status: 'processing', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 8 },
    //     { id: '4', name: 'Sports Equipment', num: '25470', createdAt: '2022-04-05', status: 'processing', shippingType: 'delivery', shippedAt: '2022-04-10', shippedBy: 'Jane Smith', orders: 15 },
    //     { id: '5', name: 'Electronics Bundle', num: '25471', createdAt: '2022-05-20', status: 'processing', shippingType: 'pickup', shippedAt: null, shippedBy: null, orders: 0 },
    //     { id: '6', name: 'Home Decor', num: '25472', createdAt: '2022-06-15', status: 'created', shippingType: 'delivery', shippedAt: null, shippedBy: null, orders: 6 },
    //     { id: '7', name: 'Kitchen Essentials', num: '25473', createdAt: '2022-07-01', status: 'shipped', shippingType: 'pickup', shippedAt: null, shippedBy: null, orders: 9 },
    //     { id: '8', name: 'Garden Tools', num: '25474', createdAt: '2022-08-10', status: 'shipped', shippingType: 'delivery', shippedAt: '2022-08-15', shippedBy: 'Mike Johnson', orders: 7 },
    // ]);

    useEffect(() => {
        dispatch(fetchCollectionsByWarehouseId(warehouse.id));
    }, [dispatch, warehouse.id]);

    const onRefresh = useCallback(() => {
        dispatch(fetchCollectionsByWarehouseId(warehouse.id));
    }, [dispatch]);



    function SwipeableCollectionRow({ item, onPress }: { item: any; onPress: () => void }) {
        const swipeRef = useRef<Swipeable>(null);
        const theme = useTheme();

        const printCollection = () => {
            console.log('🖨️ Printing:', item.name);
            swipeRef.current?.close();
        };



        const screenWidth = Dimensions.get('window').width;

        const renderRightActions = (collection: any, dragX: any) => {
            return (
                <LinearGradient
                    colors={['transparent', '#007AFF']}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                    style={{
                        width: screenWidth,
                        maxWidth: 80,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        paddingRight: 20,
                        borderRadius: 8,
                        marginVertical: 2,
                    }}
                >
                    <AntDesign name="printer" size={24} color="#fff" />
                </LinearGradient>
            );
        };

        return (
            <Swipeable
                ref={swipeRef}
                renderRightActions={(progress, dragX) => renderRightActions(item, dragX)}
                onSwipeableOpen={printCollection}
            >
                <CollectionCard collection={item} onPress={onPress} />
            </Swipeable>
        );
    }

    return (
        <ThemedView>
            <View
                style={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    marginBottom: 10,
                }}
            >
                <TouchableOpacity onPress={() => navigation.goBack()} style={{ height: 34 }}>
                    <AntDesign name="arrowleft" size={30} color={theme.text} />
                </TouchableOpacity>
                {!loading ? <ThemedText style={{ fontSize: 24, marginBottom: 16, height: 30 }} variant="title">
                    {collections.length} Collections
                </ThemedText> : <PageTitleSkeleton />
                }
            </View>


            <FlatList
                data={collections}
                keyExtractor={(item) => item.id.toFixed(0)}
                ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
                renderItem={({ item }) => (
                    <SwipeableCollectionRow
                        item={item}
                        onPress={() => navigation.navigate('CollectionDetails', { collection: item })} />
                )}
                refreshing={loading}
                onRefresh={onRefresh}
            />
        </ThemedView>
    );
}
