import React, { useState } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { useDispatch } from 'react-redux';
import { loginSuccess } from './authSlice';
import axiosClient from '../../api/axiosClient';
import ThemedView from '../../components/ThemedView';
import ThemedTextInput from '../../components/ThemedTextInput';
import ThemedButton from '../../components/ThemedButton';
import ThemedText from '../../components/ThemedText';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import useTheme from '../../theme/useTheme';
import { saveAuthData } from '../../utils/authStorage';

export default function LoginScreen() {
    const dispatch = useDispatch();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const theme = useTheme();

    const handleLogin = async () => {

        try {
            setLoading(true);
            const response = await axiosClient.post('/login', { email, password });
            const { token, result } = response.data;
            console.log(response.data);

            // Store token and user securely
            await saveAuthData(token, result);

            // Update Redux
            dispatch(loginSuccess({ token, user: result }));
        } catch (error: any) {
            console.error('Login failed:', error);
            Alert.alert('Login Error', error.response?.data?.message || 'Something went wrong.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <ThemedView>
            <View style={styles.header}>
                <FontAwesome6 name="cubes-stacked" size={80} color={theme.text} />
                <ThemedText variant="title" style={styles.title}>Welcome back!</ThemedText>
                <ThemedText variant="subtitle">Please log in to access your collections</ThemedText>
            </View>

            <ThemedTextInput
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
            />
            <ThemedTextInput
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                isPassword
            />

            <ThemedButton title={loading ? 'Logging in...' : 'Login'} onPress={handleLogin} disabled={loading} />
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    header: {
        marginVertical: 20,
        alignItems: 'center',
        flex: 0.5,
    },
    title: {
        marginTop: 8,
    },
});
