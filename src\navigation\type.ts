export type OrderProduct = {
    id: number;
    name: string;
    quantity: number;
    status: string;
};

export type Order = {
    id: number;
    orderNum: string;
    orderCode: string;
    status: string;
    sku: string;
    goodsDescription: string;
    ordersProducts: OrderProduct[];
    [key: string]: any; // allow extra fields
};

export type RootStackParamList = {
    Splash: undefined;
    Login: undefined;
    Warehouses: undefined;
    Collections: { warehouse: any };
    CollectionDetails: { collection: any };
    Fulfillment: { collectionId: string; orders: Order[] };
    OrderDetailsModal: { order: Order };
};