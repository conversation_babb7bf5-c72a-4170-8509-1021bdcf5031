// src/features/collections/collectionsThunks.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';

export const fetchCollectionsByWarehouseId = createAsyncThunk(
    'collections/fetchByWarehouseId',
    async (warehouseId: number, { rejectWithValue }) => {
        console.log('Fetching collections for warehouse ID:', warehouseId);

        try {
            const response = await axiosClient.get(`/warehouse/${warehouseId}/collections`);

            return response.data; // Adjust depending on API structure
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching collections');
        }
    }
);
