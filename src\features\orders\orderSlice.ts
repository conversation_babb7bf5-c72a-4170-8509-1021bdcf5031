
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { store } from '../../app/store';
import { Order } from '../../navigation/type';


// Define the shape of the slice state
interface OrderState {
    orders: Order[];
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: OrderState = {
    orders: [],
    loading: false,
    error: null,
};

export const toggleProductStatus = createAsyncThunk(
    'orders/toggleProductStatus',
    async ({ productId }: { productId: number }, { rejectWithValue }) => {
        try {

            const response = await axiosClient.put(`/products/${productId}/gather`);


            return response.data;
        } catch (error: any) {

            return rejectWithValue(error.response?.data?.message || 'Failed to toggle product status');
        }
    }
);

// Async thunk to fetch orders
export const fetchOrders = createAsyncThunk(
    'orders/fetchOrders',
    async (collectionId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/collection/${collectionId}/orders`);

            return response.data; // should be an array of orders
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch orders');
        }
    }
);

// Slice
const orderSlice = createSlice({
    name: 'orders',
    initialState,
    reducers: {
        // add your own reducers here if needed
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchOrders.fulfilled, (state, action: PayloadAction<Order[]>) => {
                state.loading = false;
                state.orders = action.payload;
            })
            .addCase(fetchOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            });
    },
});

// Export the reducer
export default orderSlice.reducer;



